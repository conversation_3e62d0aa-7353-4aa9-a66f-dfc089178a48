import { PrismaService } from 'nestjs-prisma';
import { LoginDto, RegisterDto } from './dto/create-auth.dto';
import { MailService } from 'src/mail/mail.service';
export declare class AuthService {
    private readonly prisma;
    private readonly mailService;
    constructor(prisma: PrismaService, mailService: MailService);
    login(dto: LoginDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | {
        id: number;
        email: string;
        role: string;
        name: string;
        phone: null;
        profilePic: null;
        location: null;
        skills: never[];
        about: null;
    }>;
    register(dto: RegisterDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        createdAt: Date;
        updatedAt: Date;
        resetToken: string | null;
        resetTokenExpiry: Date | null;
    }>;
    findAll(): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }[] | {
        id: number;
        email: string;
        role: string;
        name: string;
        phone: null;
        profilePic: null;
        location: null;
        skills: never[];
        about: null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    update(id: number, updateData: any): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    remove(id: number): Promise<{
        id: number;
    }>;
    getUserById(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    getUserByEmail(email: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    forgotPassword(email: string): Promise<{
        message: string;
    }>;
    resetPassword(token: string, newPass: string, confirmPass: string): Promise<{
        message: string;
    }>;
    changePassword(email: string, currentPassword: string, newPassword: string): Promise<{
        message: string;
    }>;
}
